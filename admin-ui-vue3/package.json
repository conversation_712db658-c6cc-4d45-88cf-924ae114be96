{"name": "admin-ui-vue3", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@shadcn/ui": "^0.0.4", "@tailwindcss/vite": "^4.1.8", "@tanstack/vue-table": "^8.21.3", "@types/clipboard": "^2.0.1", "@types/js-cookie": "^3.0.6", "@vee-validate/zod": "^4.15.1", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clipboard": "^2.0.11", "clsx": "^2.1.1", "js-cookie": "^3.0.5", "lucide-vue-next": "^0.513.0", "pinia-plugin-persistedstate": "^4.3.0", "postcss": "^8.5.4", "reka-ui": "^2.3.1", "screenfull": "^6.0.2", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.8", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.4", "vee-validate": "^4.15.1", "vue": "^3.5.13", "vue-sonner": "^2.0.0", "zod": "^3.25.67"}, "devDependencies": {"@types/axios": "^0.9.36", "@types/node": "^22.15.30", "@vitejs/plugin-vue": "^5.2.3", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.7.0", "@vueuse/core": "^13.4.0", "@vueuse/head": "^2.0.0", "eslint": "^9.28.0", "eslint-plugin-vue": "^10.2.0", "pinia": "^3.0.3", "prettier": "^3.5.3", "typescript": "~5.8.3", "vite": "^6.3.5", "vue-router": "^4.5.1", "vue-tsc": "^2.2.8"}}