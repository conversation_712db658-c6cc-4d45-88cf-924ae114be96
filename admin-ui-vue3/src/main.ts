import { initializeResponsive } from '@/composables/useResponsive'
import { initializeTheme } from '@/composables/useTheme'
import { createApp } from 'vue'
import App from './App.vue'
import './assets/globals.css'
import router from './router'
import pinia from './store'

const app = createApp(App)
app.use(pinia)
app.use(router)

// 初始化主题系统
initializeTheme()

// 初始化响应式系统
initializeResponsive()

app.mount('#app')
