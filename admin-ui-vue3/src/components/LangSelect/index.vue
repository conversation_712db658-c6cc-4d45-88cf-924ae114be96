<template>
  <DropdownMenu>
    <DropdownMenuTrigger as-child>
      <Button variant="ghost" size="icon" class="international">
        <Languages class="h-5 w-5" />
        <span class="sr-only">切换语言</span>
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent align="end">
      <DropdownMenuItem
        :disabled="language === 'zh'"
        @click="handleSetLanguage('zh')"
      >
        <span>中文</span>
      </DropdownMenuItem>
      <DropdownMenuItem
        :disabled="language === 'en'"
        @click="handleSetLanguage('en')"
      >
        <span>English</span>
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useAppStore } from '@/store/app'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Languages } from 'lucide-vue-next'
import { toast } from '@/hooks/use-toast'

const { locale } = useI18n()
const appStore = useAppStore()

const language = computed(() => appStore.language)

const handleSetLanguage = (lang: string) => {
  locale.value = lang
  appStore.setLanguage(lang)
  
  toast({
    title: "语言切换成功",
    description: `已切换到${lang === 'zh' ? '中文' : 'English'}`,
  })
}
</script>

<style scoped>
.international {
  @apply transition-colors hover:bg-accent hover:text-accent-foreground;
}
</style>
