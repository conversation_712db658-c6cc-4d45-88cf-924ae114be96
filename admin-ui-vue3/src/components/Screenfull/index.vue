<template>
  <Button
    variant="ghost"
    size="icon"
    class="screenfull-button"
    @click="handleClick"
  >
    <Maximize v-if="!isFullscreen" class="h-5 w-5" />
    <Minimize v-else class="h-5 w-5" />
    <span class="sr-only">{{ isFullscreen ? '退出全屏' : '进入全屏' }}</span>
  </Button>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import screenfull from 'screenfull'
import { Button } from '@/components/ui/button'
import { Maximize, Minimize } from 'lucide-vue-next'
import { toast } from '@/hooks/use-toast'

const isFullscreen = ref(false)

const handleClick = () => {
  if (!screenfull.isEnabled) {
    toast({
      title: "全屏功能不可用",
      description: "您的浏览器不支持全屏功能",
      variant: "destructive",
    })
    return
  }
  
  screenfull.toggle()
}

const handleChange = () => {
  isFullscreen.value = screenfull.isFullscreen
}

onMounted(() => {
  if (screenfull.isEnabled) {
    screenfull.on('change', handleChange)
    isFullscreen.value = screenfull.isFullscreen
  }
})

onUnmounted(() => {
  if (screenfull.isEnabled) {
    screenfull.off('change', handleChange)
  }
})
</script>

<style scoped>
.screenfull-button {
  @apply transition-colors hover:bg-accent hover:text-accent-foreground;
}
</style>
