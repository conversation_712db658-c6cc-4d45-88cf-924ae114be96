<template>
  <DropdownMenu>
    <DropdownMenuTrigger as-child>
      <Button variant="ghost" size="icon" class="theme-toggle">
        <Sun v-if="colorScheme === 'light'" class="h-5 w-5" />
        <Moon v-else class="h-5 w-5" />
        <span class="sr-only">切换主题</span>
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent align="end" class="w-56">
      <DropdownMenuLabel>主题设置</DropdownMenuLabel>
      <DropdownMenuSeparator />
      
      <!-- 主题模式选择 -->
      <DropdownMenuLabel class="text-xs text-muted-foreground">
        显示模式
      </DropdownMenuLabel>
      <DropdownMenuRadioGroup v-model="currentTheme">
        <DropdownMenuRadioItem value="light">
          <Sun class="h-4 w-4 mr-2" />
          浅色模式
        </DropdownMenuRadioItem>
        <DropdownMenuRadioItem value="dark">
          <Moon class="h-4 w-4 mr-2" />
          深色模式
        </DropdownMenuRadioItem>
        <DropdownMenuRadioItem value="system">
          <Monitor class="h-4 w-4 mr-2" />
          跟随系统
        </DropdownMenuRadioItem>
      </DropdownMenuRadioGroup>
      
      <DropdownMenuSeparator />
      
      <!-- 主题颜色选择 -->
      <DropdownMenuLabel class="text-xs text-muted-foreground">
        主题颜色
      </DropdownMenuLabel>
      <div class="p-2">
        <div class="grid grid-cols-4 gap-2 mb-3">
          <button
            v-for="color in presetColors.slice(0, 8)"
            :key="color.name"
            :class="[
              'w-8 h-8 rounded-md border-2 transition-all hover:scale-110',
              currentColor === color.value 
                ? 'border-foreground ring-2 ring-ring ring-offset-2' 
                : 'border-transparent hover:border-muted-foreground'
            ]"
            :style="{ backgroundColor: color.value }"
            @click="setThemeColor(color.value)"
            :title="color.name"
          />
        </div>
        
        <!-- 自定义颜色选择器 -->
        <div class="flex items-center gap-2">
          <input
            v-model="customColor"
            type="color"
            class="w-8 h-8 rounded border border-input cursor-pointer"
            @change="setThemeColor(customColor)"
            title="自定义颜色"
          />
          <Button
            variant="outline"
            size="sm"
            @click="resetThemeColor"
            class="text-xs"
          >
            重置
          </Button>
        </div>
      </div>
      
      <DropdownMenuSeparator />
      
      <!-- 快捷操作 -->
      <DropdownMenuItem @click="openThemeSettings">
        <Settings class="h-4 w-4 mr-2" />
        更多设置
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Sun, Moon, Monitor, Settings } from 'lucide-vue-next'
import { useTheme, type Theme } from '@/composables/useTheme'
import { toast } from '@/hooks/use-toast'

const {
  theme,
  colorScheme,
  setTheme,
  setThemeColor,
  resetThemeColor,
  getCurrentThemeColor,
  presetColors,
} = useTheme()

const customColor = ref(getCurrentThemeColor())
const currentColor = ref(getCurrentThemeColor())

const currentTheme = computed({
  get: () => theme.value,
  set: (value: Theme) => {
    setTheme(value)
    toast({
      title: "主题已切换",
      description: getThemeDescription(value),
    })
  }
})

const handleColorChange = (color: string) => {
  setThemeColor(color)
  currentColor.value = color
  customColor.value = color
  
  toast({
    title: "主题颜色已更新",
    description: "新的颜色主题已应用",
  })
}

const handleResetColor = () => {
  resetThemeColor()
  const defaultColor = presetColors[0].value
  currentColor.value = defaultColor
  customColor.value = defaultColor
  
  toast({
    title: "主题颜色已重置",
    description: "已恢复为默认颜色主题",
  })
}

const openThemeSettings = () => {
  // 这里可以打开更详细的主题设置页面
  toast({
    title: "功能开发中",
    description: "更多主题设置功能正在开发中",
  })
}

const getThemeDescription = (theme: Theme): string => {
  const descriptions = {
    light: '已切换到浅色模式',
    dark: '已切换到深色模式',
    system: '已设置为跟随系统主题'
  }
  return descriptions[theme]
}

// 监听当前颜色变化
watch(() => getCurrentThemeColor(), (newColor) => {
  currentColor.value = newColor
  customColor.value = newColor
}, { immediate: true })

// 暴露方法供外部调用
defineExpose({
  setThemeColor: handleColorChange,
  resetThemeColor: handleResetColor,
})
</script>

<style scoped>
.theme-toggle {
  @apply transition-colors hover:bg-accent hover:text-accent-foreground;
}

/* 自定义颜色选择器样式 */
input[type="color"] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-color: transparent;
  border: none;
  cursor: pointer;
}

input[type="color"]::-webkit-color-swatch-wrapper {
  padding: 0;
  border: none;
  border-radius: 6px;
}

input[type="color"]::-webkit-color-swatch {
  border: none;
  border-radius: 4px;
}

input[type="color"]::-moz-color-swatch {
  border: none;
  border-radius: 4px;
}
</style>
